/**
 * ChatLo Admin Module API Types
 * Defines the contract for plugin architecture and admin operations
 */

// Core Plugin Interface
export interface ChatLoPlugin {
  initialize(config: PluginConfig): Promise<void>
  process(input: ProcessingInput): Promise<ProcessingOutput>
  validate(testCase: TestCase): Promise<ValidationResult>
  
  metadata: {
    name: string
    version: string
    capabilities: string[]
    requirements: SystemRequirements
  }
}

// Configuration Types
export interface PluginConfig {
  apiKey?: string
  modelSettings: ModelSettings
  systemPrompts: SystemPrompts
  pipelineConfig: PipelineConfig
}

export interface ModelSettings {
  temperature: number
  maxTokens: number
  topP: number
  topK: number
  frequencyPenalty: number
  presencePenalty: number
}

export interface SystemPrompts {
  extraction: string
  analysis: string
  validation: string
  fallback: string
}

export interface PipelineConfig {
  stages: PipelineStage[]
  errorHandling: ErrorHandlingConfig
  performance: PerformanceConfig
}

export interface PipelineStage {
  name: string
  type: 'extraction' | 'analysis' | 'validation' | 'transformation'
  config: Record<string, any>
  enabled: boolean
}

// Processing Types
export interface ProcessingInput {
  content: string
  contentType: 'text' | 'markdown' | 'html' | 'pdf'
  language: string
  metadata?: Record<string, any>
}

export interface ProcessingOutput {
  entities: ExtractedEntity[]
  topics: ExtractedTopic[]
  artifacts: ExtractedArtifact[]
  confidence: number
  domain: string
  processingMetadata: ProcessingMetadata
}

export interface ExtractedEntity {
  name: string
  type: 'person' | 'place' | 'concept' | 'technology' | 'organization' | 'other'
  confidence: number
  context?: string
  metadata?: Record<string, any>
}

export interface ExtractedTopic {
  name: string
  relevance: number
  category: string
  keywords?: string[]
  metadata?: Record<string, any>
}

export interface ExtractedArtifact {
  type: 'image' | 'code' | 'markdown' | 'mermaid' | 'html' | 'json' | 'summary' | 'insights'
  content: string
  confidence: number
  metadata?: Record<string, any>
}

// Testing Types
export interface TestCase {
  id: string
  name: string
  description: string
  input: ProcessingInput
  expectedOutput: Partial<ProcessingOutput>
  tags: string[]
  priority: 'low' | 'medium' | 'high' | 'critical'
}

export interface TestSuite {
  id: string
  name: string
  description: string
  testCases: TestCase[]
  configuration: TestConfiguration
}

export interface TestConfiguration {
  models: string[]
  languages: string[]
  contentTypes: string[]
  performanceThresholds: PerformanceThresholds
}

export interface ValidationResult {
  testCaseId: string
  passed: boolean
  score: number
  metrics: TestMetrics
  errors?: string[]
  warnings?: string[]
}

export interface TestMetrics {
  accuracy: number
  precision: number
  recall: number
  f1Score: number
  processingTime: number
  memoryUsage: number
}

// System Types
export interface SystemRequirements {
  minMemory: number
  minCpu: number
  requiredModels: string[]
  optionalModels: string[]
  dependencies: string[]
}

export interface ErrorHandlingConfig {
  retryAttempts: number
  timeoutMs: number
  fallbackStrategy: 'skip' | 'retry' | 'fallback'
  logLevel: 'error' | 'warn' | 'info' | 'debug'
}

export interface PerformanceConfig {
  maxConcurrentRequests: number
  requestTimeoutMs: number
  cacheEnabled: boolean
  cacheTtlMs: number
}

export interface PerformanceThresholds {
  maxProcessingTimeMs: number
  minAccuracy: number
  minConfidence: number
  maxMemoryUsageMb: number
}

export interface ProcessingMetadata {
  version: string
  method: string
  timestamp: string
  modelUsed?: string
  processingTime?: number
  memoryUsage?: number
  [key: string]: any
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  timestamp: string
  requestId: string
}

export interface DeploymentResult {
  success: boolean
  deploymentId: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  message: string
  endpoint?: string
  version?: string
  timestamp: string
}

export interface ModelInfo {
  id: string
  name: string
  provider: string
  type: string
  contextWindow: number
  pricing: { input: number; output: number }
  capabilities: string[]
  status: 'available' | 'loading' | 'error' | 'local'
}

export interface ModelPerformance {
  averageLatency: number
  throughput: number
  accuracy: number
  memoryUsage: number
  lastUpdated: string
}

// Admin Operations
export interface AdminOperation {
  id: string
  type: 'deploy' | 'rollback' | 'test' | 'configure'
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: number
  logs: string[]
  metadata: Record<string, any>
  createdAt: string
  updatedAt: string
}

export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy'
  components: ComponentHealth[]
  metrics: SystemMetrics
  lastCheck: string
}

export interface ComponentHealth {
  name: string
  status: 'healthy' | 'degraded' | 'unhealthy'
  message?: string
  metrics?: Record<string, number>
}

export interface SystemMetrics {
  cpuUsage: number
  memoryUsage: number
  diskUsage: number
  networkLatency: number
  activeConnections: number
  requestsPerSecond: number
}
