import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'

import { useAppStore } from '../store'
import { useVerticalMode } from '../hooks/useVerticalMode'
import VerticalHamburgerMenu from './VerticalHamburgerMenu'
import { ICONS } from './Icons/index'

interface AppTopBarProps {
  className?: string
}

const AppTopBar: React.FC<AppTopBarProps> = ({ className = '' }) => {
  const navigate = useNavigate()
  const { isPrivateMode, togglePrivateMode } = useAppStore()
  const [currentNewsIndex, setCurrentNewsIndex] = useState(0)
  const isVerticalMode = useVerticalMode()

  // Sample news items - this could be fetched from a service
  const newsItems = [
    "New AI model available - DeepSeek V3 Pro",
    "ChatLo v2.1 released with enhanced features",
    "Improved local model support now available"
  ]

  const handlePreviousNews = () => {
    setCurrentNewsIndex((prev) => 
      prev === 0 ? newsItems.length - 1 : prev - 1
    )
  }

  const handleNextNews = () => {
    setCurrentNewsIndex((prev) => 
      prev === newsItems.length - 1 ? 0 : prev + 1
    )
  }

  const handlePrivateModeToggle = () => {
    togglePrivateMode()
  }

  const handleUserProfile = () => {
    // Navigate to user profile or show profile menu
    console.log('User profile clicked')
  }

  const handleSettings = () => {
    navigate('/settings')
  }

  return (
    <div className={`h-12 glass border-b border-white/10 flex items-center px-4 bg-gradient-to-r from-primary/10 to-secondary/10 ${className}`}>
      {/* Logo */}
      <div className="flex items-center -ml-1">
        <img src="/logo_v3_bkg.png" alt="Chatlo Logo" className="w-[136px] h-[41.5px]" />
      </div>
      
      {/* News Update Center - Hidden in vertical mode */}
      {!isVerticalMode && (
        <div className="flex-1 flex items-center justify-center ml-12 mr-[calc(256px-48px)]">
          <div className="flex items-center gap-2 glass-subtle rounded-lg px-3 py-1 w-full max-w-md">
            <button 
              onClick={handlePreviousNews}
              className="p-1 hover:bg-white/10 rounded transition-colors"
            >
              <FontAwesomeIcon icon={ICONS.chevronLeft} className="text-gray-300 text-xs" />
            </button>
            <div className="flex items-center gap-2 flex-1">
              <FontAwesomeIcon icon={ICONS.bell} className="text-supplement2 text-xs" />
              <span className="text-xs text-supplement1">{newsItems[currentNewsIndex]}</span>
            </div>
            <button 
              onClick={handleNextNews}
              className="p-1 hover:bg-white/10 rounded transition-colors"
            >
              <FontAwesomeIcon icon={ICONS.chevronRight} className="text-gray-300 text-xs" />
            </button>
          </div>
        </div>
      )}
      
      {/* Right Side Controls */}
      <div className="flex items-center gap-3 ml-auto">
        {/* Hamburger Menu for Vertical Mode */}
        {isVerticalMode && (
          <VerticalHamburgerMenu />
        )}
        
        {/* Regular Controls - Hidden in vertical mode */}
        {!isVerticalMode && (
          <>
            {/* Private Mode Toggle */}
            <div className="flex items-center gap-2">
              <span className="text-xs text-supplement1">Private</span>
              <button 
                onClick={handlePrivateModeToggle}
                className={`relative inline-flex h-4 w-7 items-center rounded-full transition-colors ${
                  isPrivateMode
                    ? 'bg-secondary/80 glow-secondary'
                    : 'bg-gray-600'
                }`}
              >
                <span 
                  className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                    isPrivateMode ? 'translate-x-3.5' : 'translate-x-0.5'
                  }`}
                />
              </button>
            </div>
            
            {/* User Icon */}
            <button
              onClick={handleUserProfile}
              className="p-2 hover:bg-white/10 rounded-lg transition-colors group relative"
            >
              <FontAwesomeIcon icon={ICONS.user} className="text-supplement1 text-sm" />
              <div className="absolute top-12 left-1/2 transform -translate-x-1/2 glass text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                User Profile
              </div>
            </button>

            {/* Settings Icon */}
            <button
              onClick={handleSettings}
              className="p-2 hover:bg-white/10 rounded-lg transition-colors group relative"
            >
              <FontAwesomeIcon icon={ICONS.cog} className="text-supplement1 text-sm" />
              <div className="absolute top-12 left-1/2 transform -translate-x-1/2 glass text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                Settings
              </div>
            </button>
          </>
        )}
      </div>
    </div>
  )
}

export default AppTopBar
