import React from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'

interface WindowTopBarProps {
  className?: string
}

// Custom SVG Icons for Aspect Ratios
const Aspect16_9Icon: React.FC<{ className?: string }> = ({ className = '' }) => (
  <svg 
    width="12" 
    height="12" 
    viewBox="0 0 100 100" 
    className={className}
    style={{ fill: 'currentColor' }}
  >
    <g transform="matrix(3.25973,0,0,0.585195,-40.4187,-0.704001)">
      <rect x="13.72" y="7.798" width="28.036" height="87.917" style={{ fill: 'currentColor' }}/>
    </g>
    <g transform="matrix(1.81481,0,0,0.390562,-79.0355,58.2122)">
      <path d="M45.923,31.899L45.923,1.458L51.524,1.458C51.524,5.061 50.894,7.986 50.119,7.986L47.327,7.986L47.327,25.372C47.327,28.975 46.698,31.899 45.923,31.899ZM68.31,1.458C68.31,5.061 67.68,7.986 66.905,7.986L58.512,7.986C57.737,7.986 57.107,5.061 57.107,1.458L68.31,1.458ZM85.095,1.458C85.095,5.061 84.466,7.986 83.691,7.986L75.298,7.986C74.522,7.986 73.893,5.061 73.893,1.458L85.095,1.458ZM96.28,31.899C95.504,31.899 94.875,28.975 94.875,25.372L94.875,7.986L92.083,7.986C91.308,7.986 90.679,5.061 90.679,1.458L96.28,1.458L96.28,31.899ZM90.679,97.113C90.679,93.511 91.308,90.586 92.083,90.586L94.875,90.586L94.875,73.199C94.875,69.597 95.504,66.672 96.28,66.672L96.28,97.113L90.679,97.113ZM73.893,97.113C73.893,93.511 74.522,90.586 75.298,90.586L83.69,90.586C84.466,90.586 85.095,93.511 85.095,97.113L73.893,97.113ZM57.107,97.113C57.107,93.511 57.737,90.586 58.512,90.586L66.905,90.586C67.68,90.586 68.309,93.511 68.309,97.113L57.107,97.113ZM45.923,66.672C46.698,66.672 47.327,69.597 47.327,73.199L47.327,90.586L50.119,90.586C50.894,90.586 51.524,93.511 51.524,97.113L45.923,97.113L45.923,66.672Z" style={{ fill: 'currentColor' }}/>
    </g>
  </svg>
)

const Aspect10_16Icon: React.FC<{ className?: string }> = ({ className = '' }) => (
  <svg 
    width="12" 
    height="12" 
    viewBox="0 0 100 100" 
    className={className}
    style={{ fill: 'currentColor' }}
  >
    <g transform="matrix(1.49033,0,0,1.07809,-16.6679,-5.5196)">
      <rect x="13.72" y="7.798" width="28.036" height="87.917" style={{ fill: 'currentColor' }}/>
    </g>
    <g transform="matrix(1.05342,0,0,0.948123,-6.25459,4.02073)">
      <rect x="45.923" y="1.458" width="50.357" height="95.655" style={{ fill: 'none', stroke: 'currentColor', strokeWidth: '2.54px', strokeDasharray: '12.72,12.72,0,0,0,0' }}/>
    </g>
  </svg>
)

const WindowTopBar: React.FC<WindowTopBarProps> = ({ className = '' }) => {
  const handleMinimize = () => {
    if (window.electronAPI?.windowControls) {
      window.electronAPI.windowControls.minimize()
    }
  }

  const handleMaximize = () => {
    if (window.electronAPI?.windowControls) {
      window.electronAPI.windowControls.maximize()
    }
  }

  const handleClose = () => {
    if (window.electronAPI?.windowControls) {
      window.electronAPI.windowControls.close()
    }
  }

  const handleAspect16_9 = () => {
    if (window.electronAPI?.windowControls) {
      window.electronAPI.windowControls.aspect16_9()
    }
  }

  const handleAspect10_16 = () => {
    if (window.electronAPI?.windowControls) {
      window.electronAPI.windowControls.aspect10_16()
    }
  }

  return (
    <div
      className={`h-6 glass-subtle flex items-center justify-between px-2 ${className}`}
      style={{ WebkitAppRegion: 'drag' } as React.CSSProperties}
    >
      {/* Empty left side for dragging */}
      <div className="flex-1"></div>

      {/* Window Controls */}
      <div
        className="flex items-center gap-2"
        style={{ WebkitAppRegion: 'no-drag' } as React.CSSProperties}
      >
        <button
          onClick={handleMinimize}
          className="w-5 h-5 flex items-center justify-center hover:bg-white/10 rounded transition-colors"
          title="Minimize"
        >
          <FontAwesomeIcon icon={ICONS.minus} className="text-gray-300 text-xs" />
        </button>
        
        {/* Aspect Ratio Controls */}
        <button
          onClick={handleAspect16_9}
          className="w-5 h-5 flex items-center justify-center hover:bg-white/10 rounded transition-colors"
          title="16:9 Aspect Ratio (Regular Working Mode)"
        >
          <Aspect16_9Icon className="text-gray-300" />
        </button>
        
        <button
          onClick={handleAspect10_16}
          className="w-5 h-5 flex items-center justify-center hover:bg-white/10 rounded transition-colors"
          title="10:16 Aspect Ratio (Vertical Co-pilot Mode)"
        >
          <Aspect10_16Icon className="text-gray-300" />
        </button>
        
        <button
          onClick={handleMaximize}
          className="w-5 h-5 flex items-center justify-center hover:bg-white/10 rounded transition-colors"
          title="Maximize/Restore"
        >
          <FontAwesomeIcon icon={ICONS.expand} className="text-gray-300 text-xs" />
        </button>
        <button
          onClick={handleClose}
          className="w-5 h-5 flex items-center justify-center hover:bg-red-500/20 rounded transition-colors"
          title="Close"
        >
          <FontAwesomeIcon icon={ICONS.xmark} className="text-gray-300 hover:text-white text-xs" />
        </button>
      </div>
    </div>
  )
}

export default WindowTopBar
