import React from 'react'
import <PERSON>actDOM from 'react-dom/client'
import { AdminDashboard } from './components/AdminDashboard'
import './index.css'

// Initialize ChatLo Admin Module
console.log('🏛️ ChatLo Admin Module v1.0.0 - Initializing...')

// Check if running in popup mode or standalone
const isPopup = window.opener !== null
const isStandalone = window.location.search.includes('standalone=true')

if (isPopup) {
  console.log('📱 Running in popup mode')
} else if (isStandalone) {
  console.log('🖥️ Running in standalone mode')
} else {
  console.log('🌐 Running in embedded mode')
}

// Main App Component
const App: React.FC = () => {
  const handleClose = () => {
    if (isPopup && window.opener) {
      window.close()
    } else {
      // Navigate back or show close confirmation
      if (confirm('Close ChatLo Admin Dashboard?')) {
        window.close()
      }
    }
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <AdminDashboard onClose={isPopup ? handleClose : undefined} />
    </div>
  )
}

// Render the application
ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
)

// Global error handling
window.addEventListener('error', (event) => {
  console.error('ChatLo Admin Error:', event.error)
})

window.addEventListener('unhandledrejection', (event) => {
  console.error('ChatLo Admin Unhandled Promise Rejection:', event.reason)
})

// Development helpers
if (import.meta.env?.DEV || process.env.NODE_ENV === 'development') {
  console.log('🔧 Development mode enabled')

  // Expose API for debugging
  ;(window as any).chatLoAdmin = {
    version: '1.0.0',
    mode: isPopup ? 'popup' : isStandalone ? 'standalone' : 'embedded',
    debug: {
      clearStorage: () => {
        localStorage.clear()
        sessionStorage.clear()
        console.log('Storage cleared')
      },
      getStorageInfo: () => {
        console.log('LocalStorage:', localStorage)
        console.log('SessionStorage:', sessionStorage)
      }
    }
  }
}
