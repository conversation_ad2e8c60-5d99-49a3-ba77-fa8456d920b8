import React, { useState, useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'
import { useNavigation, useNavigationShortcuts } from '../hooks/useNavigation'
import { QuickAction } from '../services/navigationService'

interface QuickActionsMenuProps {
  isOpen: boolean
  onClose: () => void
  className?: string
}

const QuickActionsMenu: React.FC<QuickActionsMenuProps> = ({
  isOpen,
  onClose,
  className = ''
}) => {
  const { getQuickActions, navigate, currentVault } = useNavigation()
  const { shortcuts } = useNavigationShortcuts()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedIndex, setSelectedIndex] = useState(0)

  const quickActions = getQuickActions()
  
  // Filter actions based on search term
  const filteredActions = quickActions.filter(action =>
    action.label.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Reset selection when actions change
  useEffect(() => {
    setSelectedIndex(0)
  }, [filteredActions.length])

  // Handle keyboard navigation
  useEffect(() => {
    if (!isOpen) return

    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault()
          setSelectedIndex(prev => 
            prev < filteredActions.length - 1 ? prev + 1 : 0
          )
          break
        case 'ArrowUp':
          event.preventDefault()
          setSelectedIndex(prev => 
            prev > 0 ? prev - 1 : filteredActions.length - 1
          )
          break
        case 'Enter':
          event.preventDefault()
          if (filteredActions[selectedIndex]) {
            handleActionClick(filteredActions[selectedIndex])
          }
          break
        case 'Escape':
          event.preventDefault()
          onClose()
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, filteredActions, selectedIndex, onClose])

  const getIconForQuickAction = (iconName: string) => {
    const iconMap: Record<string, any> = {
      'fa-search': ICONS.search,
      'fa-comment': ICONS.comment,
      'fa-upload': ICONS.upload,
      'fa-clock': ICONS.clock,
      'fa-plus': ICONS.plus,
      'fa-folder': ICONS.folder,
      'fa-gear': ICONS.settings,
      'fa-lightning': ICONS.lightning,
      'fa-keyboard': ICONS.keyboard,
      'fa-circle': ICONS.circle,
      'fa-file': ICONS.file,
      'fa-file-text': ICONS.fileText,
      'fa-file-code': ICONS.fileCode,
      'fa-file-image': ICONS.fileImage,
      'fa-file-pdf': ICONS.filePdf
    }
    return iconMap[iconName] || ICONS.lightning
  }

  const handleActionClick = (action: QuickAction) => {
    navigate(action.link)
    onClose()
  }

  const formatShortcut = (shortcut: string) => {
    return shortcut.replace('Cmd', '⌘').replace('Ctrl', 'Ctrl').replace('Alt', '⌥')
  }

  if (!isOpen) return null

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
        onClick={onClose}
      />
      
      {/* Quick Actions Modal */}
      <div className={`
        fixed top-20 left-1/2 transform -translate-x-1/2 
        w-full max-w-2xl mx-4 z-50
        bg-white dark:bg-neutral-900 
        border border-gray-200 dark:border-neutral-700
        rounded-xl shadow-2xl
        ${className}
      `}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-neutral-700">
          <div className="flex items-center space-x-2">
            <FontAwesomeIcon icon={ICONS.lightning} className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Quick Actions
            </h2>
            {currentVault && (
              <span className="text-sm text-gray-500 dark:text-gray-400">
                in {currentVault}
              </span>
            )}
          </div>
          <button
            onClick={onClose}
            className="p-1 rounded-md hover:bg-gray-100 dark:hover:bg-neutral-800 transition-colors"
          >
            <FontAwesomeIcon icon={ICONS.times} className="h-4 w-4 text-gray-500" />
          </button>
        </div>

        {/* Search Input */}
        <div className="p-4 border-b border-gray-200 dark:border-neutral-700">
          <div className="relative">
            <FontAwesomeIcon
              icon={ICONS.search}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"
            />
            <input
              type="text"
              placeholder="Search actions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-gray-50 dark:bg-neutral-800 border border-gray-200 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              autoFocus
            />
          </div>
        </div>

        {/* Actions List */}
        <div className="max-h-96 overflow-y-auto">
          {filteredActions.length > 0 ? (
            <div className="p-2">
              {filteredActions.map((action, index) => (
                <button
                  key={`${action.label}-${index}`}
                  onClick={() => handleActionClick(action)}
                  className={`
                    w-full flex items-center justify-between p-3 rounded-lg transition-colors
                    ${index === selectedIndex
                      ? 'bg-indigo-50 dark:bg-indigo-900/20 text-indigo-700 dark:text-indigo-300'
                      : 'hover:bg-gray-50 dark:hover:bg-neutral-800 text-gray-700 dark:text-gray-300'
                    }
                  `}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`
                      w-8 h-8 rounded-lg flex items-center justify-center
                      ${index === selectedIndex
                        ? 'bg-indigo-100 dark:bg-indigo-800'
                        : 'bg-gray-100 dark:bg-neutral-700'
                      }
                    `}>
                      <FontAwesomeIcon 
                        icon={getIconForQuickAction(action.icon)}
                        className="h-4 w-4"
                      />
                    </div>
                    <div className="text-left">
                      <div className="font-medium">{action.label}</div>
                      {action.badge && (
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {action.badge} items
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {action.shortcut && (
                    <div className="text-xs text-gray-500 dark:text-gray-400 font-mono bg-gray-100 dark:bg-neutral-700 px-2 py-1 rounded">
                      {formatShortcut(action.shortcut)}
                    </div>
                  )}
                </button>
              ))}
            </div>
          ) : (
            <div className="p-8 text-center text-gray-500 dark:text-gray-400">
              <FontAwesomeIcon icon={ICONS.search} className="h-8 w-8 mb-2 opacity-50" />
              <p>No actions found for "{searchTerm}"</p>
            </div>
          )}
        </div>

        {/* Footer with shortcuts */}
        <div className="p-4 border-t border-gray-200 dark:border-neutral-700 bg-gray-50 dark:bg-neutral-800/50">
          <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
            <div className="flex items-center space-x-4">
              <span className="flex items-center space-x-1">
                <kbd className="px-1.5 py-0.5 bg-white dark:bg-neutral-700 border border-gray-200 dark:border-neutral-600 rounded text-xs">↑↓</kbd>
                <span>Navigate</span>
              </span>
              <span className="flex items-center space-x-1">
                <kbd className="px-1.5 py-0.5 bg-white dark:bg-neutral-700 border border-gray-200 dark:border-neutral-600 rounded text-xs">↵</kbd>
                <span>Select</span>
              </span>
              <span className="flex items-center space-x-1">
                <kbd className="px-1.5 py-0.5 bg-white dark:bg-neutral-700 border border-gray-200 dark:border-neutral-600 rounded text-xs">Esc</kbd>
                <span>Close</span>
              </span>
            </div>
            <button
              onClick={() => {
                // Show keyboard shortcuts modal
                console.log('Show shortcuts modal')
              }}
              className="flex items-center space-x-1 hover:text-gray-700 dark:hover:text-gray-300"
            >
              <FontAwesomeIcon icon={ICONS.keyboard} className="h-3 w-3" />
              <span>Shortcuts</span>
            </button>
          </div>
        </div>
      </div>
    </>
  )
}

/**
 * Quick Actions Trigger Button
 */
interface QuickActionsTriggerProps {
  className?: string
}

export const QuickActionsTrigger: React.FC<QuickActionsTriggerProps> = ({
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false)

  // Listen for global search trigger
  useEffect(() => {
    const handleOpenSearch = () => setIsOpen(true)
    document.addEventListener('chatlo:open-search', handleOpenSearch)
    return () => document.removeEventListener('chatlo:open-search', handleOpenSearch)
  }, [])

  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className={`
          flex items-center space-x-2 px-3 py-2 
          bg-gray-100 dark:bg-neutral-800 
          hover:bg-gray-200 dark:hover:bg-neutral-700
          border border-gray-200 dark:border-neutral-600
          rounded-lg transition-colors
          ${className}
        `}
      >
        <FontAwesomeIcon icon={ICONS.search} className="h-4 w-4 text-gray-500" />
        <span className="text-sm text-gray-500">Quick actions...</span>
        <kbd className="ml-auto text-xs text-gray-400 bg-white dark:bg-neutral-700 px-1.5 py-0.5 rounded border">
          ⌘K
        </kbd>
      </button>

      <QuickActionsMenu 
        isOpen={isOpen} 
        onClose={() => setIsOpen(false)} 
      />
    </>
  )
}

export default QuickActionsMenu
