import {
  ExtractedEntity,
  ExtractedTopic,
  ExtractedArtifact,
  IntelligenceExtractionData,
  ProcessingMetadata,
  HybridExtractionInfo,
  DomainDetection,
  ContextFolder
} from '../types'
import { performanceMonitor } from './performanceMonitor'
import { BaseService } from './base'

/**
 * Intelligence Service for extracting entities, topics, and artifacts from messages
 * Optimized for baseline hardware (9th gen i7 + RTX 2060)
 */
class IntelligenceService extends BaseService {
  private readonly PROCESSING_VERSION = '1.1'

  constructor() {
    super({
      name: 'IntelligenceService',
      autoInitialize: true
    })
  }

  /**
   * Initialize the service
   */
  protected async doInitialize(): Promise<void> {
    this.logger.info('Intelligence service initialized', 'doInitialize', {
      processingVersion: this.PROCESSING_VERSION
    })
  }

  /**
   * Health check implementation
   */
  protected async doHealthCheck(): Promise<boolean> {
    try {
      // Check if performance monitor is available
      const canProcess = performanceMonitor.canProcess('immediate')
      this.logger.info('Health check completed', 'doHealthCheck', { canProcess })
      return canProcess
    } catch (error) {
      this.logger.warn('Health check failed', 'doHealthCheck', error)
      return false
    }
  }

  /**
   * Cleanup implementation
   */
  protected async doCleanup(): Promise<void> {
    this.logger.info('Intelligence service cleaned up', 'doCleanup')
  }

  /**
   * Extract intelligence using the selected LLM model (for testing)
   */
  async extractIntelligenceWithModel(
    content: string,
    modelId: string,
    modelInfo: any,
    attachments: any[] = []
  ): Promise<IntelligenceExtractionData> {
    return await this.executeOperationOrThrow(
      'extractIntelligenceWithModel',
      async () => {
        this.logger.info('Starting LLM-based intelligence extraction', 'extractIntelligenceWithModel', {
          modelName: modelInfo?.name,
          provider: modelInfo?.provider,
          contentLength: content.length,
          attachmentCount: attachments.length
        })

        try {
          // Create extraction prompt for the LLM
          const extractionPrompt = this.createExtractionPrompt(content)

          // Call the LLM model
          const llmResponse = await this.callLLMForExtraction(modelId, extractionPrompt, modelInfo)

          // Parse LLM response into structured data
          const result = await this.parseLLMResponse(llmResponse, content, attachments)

          this.logger.info('LLM extraction completed successfully', 'extractIntelligenceWithModel', {
            entitiesCount: result.entities.length,
            topicsCount: result.topics.length
          })

          return result

        } catch (error) {
          this.logger.warn('LLM extraction failed, falling back to keyword matching', 'extractIntelligenceWithModel', error)
          // Fallback to keyword-based extraction
          const fallbackResult = await this.extractIntelligence(content, attachments)
          return fallbackResult.data
        }
      },
      { modelId, contentLength: content.length, attachmentCount: attachments.length }
    )
  }

  /**
   * Assess confidence of extraction results with UTF-8 support
   */
  private assessExtractionConfidence(
    entities: ExtractedEntity[],
    topics: ExtractedTopic[],
    _content: string
  ): number {
    // Use proper Unicode-aware word counting for international languages
    const contentNormalized = _content.normalize('NFC')

    // Split by Unicode word boundaries (handles Chinese, Japanese, etc.)
    const contentWords = contentNormalized.match(/\p{L}+/gu) || []
    const wordCount = contentWords.length

    const entityDensity = entities.length / Math.max(wordCount / 10, 1) // Entities per 10 words
    const topicRelevance = topics.reduce((sum, t) => sum + t.relevance, 0) / Math.max(topics.length, 1)
    const contentCoverage = topics.length > 0 ? 0.3 : 0

    // Confidence formula: balance entity density, topic relevance, and coverage
    const confidence = Math.min(
      (entityDensity * 0.3) + (topicRelevance * 0.5) + contentCoverage,
      1.0
    )

    console.log(`📊 UTF-8 Confidence Assessment: Words=${wordCount}, Entities=${entities.length}, Topics=${topics.length}, Confidence=${confidence.toFixed(2)}`)

    return Math.max(confidence, 0.1) // Minimum 10% confidence
  }

  /**
   * Detect primary domain of content with UTF-8 support for international languages
   */
  private detectPrimaryDomain(content: string): DomainDetection {
    const domainPatterns = {
      'business_research': [
        // English
        'market', 'industry', 'analysis', 'research', 'business', 'company', 'sector',
        // Chinese Simplified
        '市场', '行业', '分析', '研究', '商业', '公司', '部门', '企业', '商务',
        // Chinese Traditional
        '市場', '行業', '分析', '研究', '商業', '公司', '部門', '企業', '商務'
      ],
      'oil_gas': [
        // English
        'oil', 'gas', 'petroleum', 'energy', 'cnpc', 'sinopec', 'crude', 'refining',
        // Chinese Simplified
        '石油', '天然气', '能源', '中石油', '中石化', '原油', '炼油', '燃料',
        // Chinese Traditional
        '石油', '天然氣', '能源', '中石油', '中石化', '原油', '煉油', '燃料'
      ],
      'technology': [
        // English
        'ai', 'software', 'development', 'programming', 'tech', 'digital', 'innovation',
        // Chinese Simplified
        '人工智能', '软件', '开发', '编程', '技术', '数字', '创新', '科技',
        // Chinese Traditional
        '人工智慧', '軟體', '開發', '編程', '技術', '數位', '創新', '科技'
      ],
      'finance': [
        // English
        'investment', 'financial', 'revenue', 'profit', 'capital', 'funding', 'valuation',
        // Chinese Simplified
        '投资', '金融', '收入', '利润', '资本', '资金', '估值', '财务',
        // Chinese Traditional
        '投資', '金融', '收入', '利潤', '資本', '資金', '估值', '財務'
      ],
      'renewable_energy': [
        // English
        'renewable', 'solar', 'wind', 'green', 'sustainable', 'clean energy',
        // Chinese Simplified
        '可再生', '太阳能', '风能', '绿色', '可持续', '清洁能源',
        // Chinese Traditional
        '可再生', '太陽能', '風能', '綠色', '可持續', '清潔能源'
      ],
      'healthcare': [
        // English
        'health', 'medical', 'pharmaceutical', 'biotech', 'clinical', 'patient',
        // Chinese Simplified
        '健康', '医疗', '制药', '生物技术', '临床', '患者', '医学',
        // Chinese Traditional
        '健康', '醫療', '製藥', '生物技術', '臨床', '患者', '醫學'
      ],
      'manufacturing': [
        // English
        'production', 'manufacturing', 'supply chain', 'operations', 'logistics',
        // Chinese Simplified
        '生产', '制造', '供应链', '运营', '物流', '工厂',
        // Chinese Traditional
        '生產', '製造', '供應鏈', '運營', '物流', '工廠'
      ],
      'general': [
        // English
        'information', 'data', 'content', 'text', 'document', 'report',
        // Chinese Simplified
        '信息', '数据', '内容', '文本', '文档', '报告',
        // Chinese Traditional
        '資訊', '數據', '內容', '文本', '文檔', '報告'
      ]
    }

    // Normalize content for UTF-8 processing (handles Chinese and other international characters)
    const contentNormalized = content.toLowerCase().normalize('NFC')
    let bestMatch: DomainDetection = { domain: 'general', confidence: 0.1, keywords: [] }

    Object.entries(domainPatterns).forEach(([domain, keywords]) => {
      const matchedKeywords = keywords.filter(keyword => {
        const keywordNormalized = keyword.toLowerCase().normalize('NFC')
        return contentNormalized.includes(keywordNormalized)
      })
      const confidence = matchedKeywords.length / keywords.length

      if (confidence > bestMatch.confidence) {
        bestMatch = { domain, confidence, keywords: matchedKeywords }
      }
    })

    return bestMatch
  }

  /**
   * Enhance extraction results using LLM (placeholder for future implementation)
   */
  private async enhanceWithLLM(
    _content: string,
    initialResults: { entities: ExtractedEntity[], topics: ExtractedTopic[] }
  ): Promise<{ entities: ExtractedEntity[], topics: ExtractedTopic[] }> {
    // Placeholder for LLM enhancement
    // In future: call local LLM with structured prompt
    console.log('LLM enhancement requested but not yet implemented')

    // For now, return enhanced confidence scores for existing results
    const enhancedEntities = initialResults.entities.map(entity => ({
      ...entity,
      confidence: Math.min(entity.confidence + 0.1, 1.0) // Slight confidence boost
    }))

    const enhancedTopics = initialResults.topics.map(topic => ({
      ...topic,
      relevance: Math.min(topic.relevance + 0.1, 1.0) // Slight relevance boost
    }))

    return { entities: enhancedEntities, topics: enhancedTopics }
  }

  /**
   * Extract intelligence data from message content using hybrid approach
   */
  async extractIntelligence(content: string, attachments?: any[]): Promise<{
    data: IntelligenceExtractionData
    metadata: ProcessingMetadata
    hybridInfo: HybridExtractionInfo
  }> {
    return await this.executeOperationOrThrow(
      'extractIntelligence',
      async () => {
        const startTime = Date.now()

        // Check if system can handle processing
        if (!performanceMonitor.canProcess('quick')) {
          this.logger.warn('System under stress, using minimal extraction', 'extractIntelligence')
          return this.extractMinimalIntelligence(content)
        }

        // Step 1: Fast keyword-based extraction
        const [entities, topics, artifacts] = await Promise.all([
          this.extractEntities(content),
          this.extractTopics(content),
          this.extractArtifacts(content, attachments)
        ])

        // Step 2: Assess extraction confidence and detect domain
        const confidence = this.assessExtractionConfidence(entities, topics, content)
        const primaryDomain = this.detectPrimaryDomain(content)

        // Step 3: Determine if enhancement needed
        const needsLLMEnhancement = confidence < 0.7
        const needsUserReview = confidence < 0.8

        let enhancedData = { entities, topics, artifacts }
        let enhancementUsed = false

        // Step 4: LLM enhancement for low confidence cases
        if (needsLLMEnhancement && performanceMonitor.canProcess('batch')) {
          try {
            const enhanced = await this.enhanceWithLLM(content, { entities, topics })
            enhancedData = { ...enhancedData, ...enhanced }
            enhancementUsed = true
          } catch (error) {
            this.logger.warn('LLM enhancement failed, using keyword results', 'extractIntelligence', error)
          }
        }

        const processingTime = Date.now() - startTime
        performanceMonitor.recordProcessingTime('quick', processingTime)

        const data: IntelligenceExtractionData = {
          entities: enhancedData.entities,
          topics: enhancedData.topics,
          artifacts: enhancedData.artifacts,
          summary: this.generateSummary(content, enhancedData.entities, enhancedData.topics)
        }

        const metadata: ProcessingMetadata = {
          extraction_time_ms: processingTime,
          model_used: enhancementUsed ? 'hybrid_llm_enhanced' : 'lightweight_nlp',
          processing_version: this.PROCESSING_VERSION
        }

        const hybridInfo: HybridExtractionInfo = {
          confidence,
          primaryDomain,
          needsUserReview,
          enhancementUsed,
          extractionMethod: enhancementUsed ? 'hybrid' : 'keyword',
          qualityIndicators: {
            entityDensity: enhancedData.entities.length / Math.max(content.split(/\s+/).length / 10, 1),
            topicRelevance: enhancedData.topics.reduce((sum, t) => sum + t.relevance, 0) / Math.max(enhancedData.topics.length, 1),
            contentCoverage: enhancedData.topics.length > 0 ? 0.3 : 0
          }
        }

        this.logger.info('Intelligence extraction completed', 'extractIntelligence', {
          processingTime,
          entitiesCount: data.entities.length,
          topicsCount: data.topics.length,
          artifactsCount: data.artifacts.length,
          confidence,
          enhancementUsed
        })

        return { data, metadata, hybridInfo }
      },
      { contentLength: content.length, attachmentCount: attachments?.length || 0 }
    )
  }

  /**
   * Extract entities using lightweight pattern matching
   */
  private async extractEntities(content: string): Promise<ExtractedEntity[]> {
    const entities: ExtractedEntity[] = []
    
    // Technology patterns
    const techPatterns = [
      /\b(React|Vue|Angular|Node\.js|Python|JavaScript|TypeScript|Java|C\+\+|Rust|Go|Docker|Kubernetes|AWS|Azure|GCP)\b/gi,
      /\b(API|REST|GraphQL|SQL|NoSQL|MongoDB|PostgreSQL|MySQL|Redis|Elasticsearch)\b/gi,
      /\b(AI|ML|LLM|GPT|Claude|Gemini|ChatGPT|OpenAI|Anthropic|Google)\b/gi
    ]

    techPatterns.forEach(pattern => {
      const matches = content.match(pattern) || []
      matches.forEach(match => {
        if (!entities.find(e => e.text.toLowerCase() === match.toLowerCase())) {
          entities.push({
            text: match,
            type: 'technology',
            confidence: 0.8
          })
        }
      })
    })

    // Concept patterns
    const conceptPatterns = [
      /\b(performance|optimization|security|scalability|architecture|design|implementation|deployment|testing|debugging)\b/gi,
      /\b(database|frontend|backend|fullstack|microservices|serverless|cloud|infrastructure)\b/gi
    ]

    conceptPatterns.forEach(pattern => {
      const matches = content.match(pattern) || []
      matches.forEach(match => {
        if (!entities.find(e => e.text.toLowerCase() === match.toLowerCase())) {
          entities.push({
            text: match,
            type: 'concept',
            confidence: 0.7
          })
        }
      })
    })

    // Business & Industry Entity Patterns
    const businessPatterns = [
      // Oil & Gas Companies
      /\b(CNPC|Sinopec|CNOOC|PetroChina|Shell|BP|ExxonMobil|Chevron|Total|Saudi Aramco)\b/gi,
      // Geographic entities
      /\b(China|USA|Russia|Saudi Arabia|Iran|Iraq|UAE|Kuwait|Qatar|Norway)\b/gi,
      // Business concepts
      /\b(market share|production capacity|import dependency|investment|revenue|profit|valuation|IPO)\b/gi,
      // Industry terms
      /\b(petroleum|crude oil|natural gas|refining|upstream|downstream|midstream|drilling)\b/gi
    ]

    businessPatterns.forEach(pattern => {
      const matches = content.match(pattern) || []
      matches.forEach(match => {
        if (!entities.find(e => e.text.toLowerCase() === match.toLowerCase())) {
          const type = pattern.source.includes('CNPC|Sinopec') ? 'organization' :
                      pattern.source.includes('China|USA') ? 'place' :
                      pattern.source.includes('market share') ? 'concept' :
                      'other'
          entities.push({
            text: match,
            type,
            confidence: 0.9
          })
        }
      })
    })

    // Technology Organization patterns
    const orgPatterns = /\b(Microsoft|Google|Apple|Amazon|Meta|OpenAI|Anthropic|GitHub|GitLab|Stack Overflow)\b/gi
    const orgMatches = content.match(orgPatterns) || []
    orgMatches.forEach(match => {
      if (!entities.find(e => e.text.toLowerCase() === match.toLowerCase())) {
        entities.push({
          text: match,
          type: 'organization',
          confidence: 0.9
        })
      }
    })

    return entities.slice(0, 10) // Limit to top 10 entities
  }

  /**
   * Extract topics using keyword clustering
   */
  private async extractTopics(content: string): Promise<ExtractedTopic[]> {
    const topics: ExtractedTopic[] = []
    
    // Define topic categories with keywords
    const topicCategories = {
      // Business & Industry Topics (prioritized for research content)
      'Oil & Gas Industry': ['oil', 'gas', 'petroleum', 'energy', 'crude', 'refining', 'drilling', 'upstream', 'downstream'],
      'Market Analysis': ['market', 'trends', 'analysis', 'research', 'study', 'sector', 'industry', 'competitive'],
      'Investment & Finance': ['investment', 'finance', 'financial', 'capital', 'funding', 'revenue', 'profit', 'valuation'],
      'Corporate Analysis': ['company', 'corporation', 'business', 'enterprise', 'organization', 'firm', 'players'],
      'China Market': ['china', 'chinese', 'beijing', 'shanghai', 'asia', 'asian', 'cnpc', 'sinopec', 'cnooc'],
      'Production & Capacity': ['production', 'capacity', 'manufacturing', 'output', 'supply', 'operations'],
      'Trade & Import': ['import', 'export', 'trade', 'dependency', 'supply chain', 'logistics', 'distribution'],
      'Technology Trends': ['technology', 'innovation', 'digital', 'automation', 'ai', 'machine learning', 'tech'],
      'Renewable Energy': ['renewable', 'solar', 'wind', 'green', 'sustainable', 'clean energy', 'carbon'],

      // Technical Topics (for development content)
      'Development': ['code', 'programming', 'development', 'coding', 'implementation', 'build', 'create'],
      'AI & Machine Learning': ['ai', 'ml', 'llm', 'model', 'training', 'inference', 'neural', 'gpt', 'claude'],
      'Performance': ['performance', 'optimization', 'speed', 'fast', 'slow', 'memory', 'cpu', 'gpu'],
      'Architecture': ['architecture', 'design', 'structure', 'pattern', 'framework', 'system', 'component'],
      'Database': ['database', 'sql', 'query', 'data', 'storage', 'table', 'index', 'schema'],
      'Security': ['security', 'authentication', 'authorization', 'encryption', 'vulnerability', 'secure'],
      'Testing': ['test', 'testing', 'unit', 'integration', 'qa', 'bug', 'debug', 'validation'],
      'Deployment': ['deployment', 'deploy', 'production', 'staging', 'release', 'ci/cd', 'docker']
    }

    const contentLower = content.toLowerCase()
    
    Object.entries(topicCategories).forEach(([topicName, keywords]) => {
      const matchedKeywords = keywords.filter(keyword => 
        contentLower.includes(keyword.toLowerCase())
      )
      
      if (matchedKeywords.length > 0) {
        const relevance = Math.min(matchedKeywords.length / keywords.length, 1)
        topics.push({
          name: topicName,
          relevance,
          keywords: matchedKeywords
        })
      }
    })

    return topics.sort((a, b) => b.relevance - a.relevance).slice(0, 5)
  }

  /**
   * Extract artifacts from content and attachments
   */
  private async extractArtifacts(content: string, attachments?: any[]): Promise<ExtractedArtifact[]> {
    const artifacts: ExtractedArtifact[] = []

    // Code blocks
    const codeBlockPattern = /```[\s\S]*?```/g
    const codeMatches = content.match(codeBlockPattern) || []
    codeMatches.forEach((match, index) => {
      const language = match.match(/```(\w+)/)?.[1] || 'unknown'
      artifacts.push({
        type: 'code',
        title: `Code snippet ${index + 1} (${language})`,
        description: `Code block containing ${language} code`
      })
    })

    // Links
    const linkPattern = /https?:\/\/[^\s]+/g
    const linkMatches = content.match(linkPattern) || []
    linkMatches.forEach((link, index) => {
      artifacts.push({
        type: 'link',
        title: `Link ${index + 1}`,
        description: link
      })
    })

    // File attachments
    if (attachments && attachments.length > 0) {
      attachments.forEach(attachment => {
        artifacts.push({
          type: 'document',
          title: attachment.filename || `Attachment ${artifacts.length + 1}`,
          description: `File attachment: ${attachment.file_type || 'unknown type'}`
        })
      })
    }

    return artifacts
  }

  /**
   * Generate a brief summary of the content
   */
  private generateSummary(content: string, entities: ExtractedEntity[], topics: ExtractedTopic[]): string {
    const contentLength = content.length
    const topEntities = entities.slice(0, 3).map(e => e.text).join(', ')
    const topTopics = topics.slice(0, 2).map(t => t.name).join(', ')
    
    let summary = `Message about ${topTopics || 'general discussion'}`
    if (topEntities) {
      summary += ` involving ${topEntities}`
    }
    summary += ` (${contentLength} characters)`
    
    return summary
  }

  /**
   * Suggest context vaults based on extracted intelligence
   */
  suggestVaults(
    extractionData: IntelligenceExtractionData, 
    availableVaults: ContextFolder[]
  ): { vault: ContextFolder; confidence: number }[] {
    const suggestions: { vault: ContextFolder; confidence: number }[] = []
    
    availableVaults.forEach(vault => {
      let confidence = 0
      
      // Match based on vault name and description
      const vaultText = `${vault.name} ${vault.description}`.toLowerCase()
      
      // Check entity matches
      extractionData.entities.forEach(entity => {
        if (vaultText.includes(entity.text.toLowerCase())) {
          confidence += entity.confidence * 0.3
        }
      })
      
      // Check topic matches
      extractionData.topics.forEach(topic => {
        if (vaultText.includes(topic.name.toLowerCase())) {
          confidence += topic.relevance * 0.4
        }
        
        // Check keyword matches
        topic.keywords.forEach(keyword => {
          if (vaultText.includes(keyword.toLowerCase())) {
            confidence += 0.1
          }
        })
      })
      
      if (confidence > 0.1) {
        suggestions.push({ vault, confidence: Math.min(confidence, 1) })
      }
    })
    
    return suggestions.sort((a, b) => b.confidence - a.confidence).slice(0, 3)
  }

  /**
   * Generate a suggested vault name based on extraction data
   */
  suggestVaultName(extractionData: IntelligenceExtractionData): string {
    const topTopic = extractionData.topics[0]
    const topEntity = extractionData.entities[0]

    if (topTopic && topEntity) {
      return `${topTopic.name} - ${topEntity.text}`
    } else if (topTopic) {
      return topTopic.name
    } else if (topEntity) {
      return topEntity.text
    } else {
      return 'New Context'
    }
  }

  /**
   * Extract minimal intelligence when system is under stress
   */
  private async extractMinimalIntelligence(content: string): Promise<{
    data: IntelligenceExtractionData
    metadata: ProcessingMetadata
    hybridInfo: HybridExtractionInfo
  }> {
    const startTime = Date.now()

    // Only extract basic keywords for minimal processing
    const words = content.toLowerCase().split(/\s+/)
    const techKeywords = ['api', 'database', 'frontend', 'backend', 'react', 'node', 'python', 'javascript']

    const entities: ExtractedEntity[] = []
    const topics: ExtractedTopic[] = []

    // Simple keyword matching
    techKeywords.forEach(keyword => {
      if (words.includes(keyword)) {
        entities.push({
          text: keyword,
          type: 'technology',
          confidence: 0.5
        })
      }
    })

    const processingTime = Date.now() - startTime
    performanceMonitor.recordProcessingTime('immediate', processingTime)

    const data: IntelligenceExtractionData = {
      entities: entities.slice(0, 3), // Limit to 3 entities
      topics,
      artifacts: [],
      summary: content.substring(0, 100) + (content.length > 100 ? '...' : '')
    }

    const metadata: ProcessingMetadata = {
      extraction_time_ms: processingTime,
      model_used: 'minimal_keywords',
      processing_version: this.PROCESSING_VERSION
    }

    const hybridInfo: HybridExtractionInfo = {
      confidence: 0.3, // Low confidence for minimal extraction
      primaryDomain: { domain: 'general', confidence: 0.2, keywords: [] },
      needsUserReview: true,
      enhancementUsed: false,
      extractionMethod: 'keyword',
      qualityIndicators: {
        entityDensity: entities.length / Math.max(content.split(/\s+/).length / 10, 1),
        topicRelevance: 0,
        contentCoverage: 0
      }
    }

    return { data, metadata, hybridInfo }
  }

  /**
   * Update vault's master.md file with pinned message content
   */
  async updateVaultMasterFile(
    vaultPath: string,
    messageContent: string,
    extractionData: IntelligenceExtractionData,
    messageId: string
  ): Promise<boolean> {
    try {
      const masterFilePath = `${vaultPath}/master.md`

      // Read existing master.md content
      const readResult = await window.electronAPI.vault.readFile(masterFilePath)
      let existingContent = readResult.success ? readResult.content || '' : ''

      // Generate section for this pinned message
      const timestamp = new Date().toISOString().split('T')[0]
      const section = this.generateMasterSection(messageContent, extractionData, messageId, timestamp)

      // Append to master.md
      const updatedContent = existingContent + '\n\n' + section

      // Write back to file
      const writeResult = await window.electronAPI.vault.writeFile(masterFilePath, updatedContent)

      return writeResult.success
    } catch (error) {
      console.error('Error updating vault master file:', error)
      return false
    }
  }

  /**
   * Generate a section for master.md from pinned message
   */
  private generateMasterSection(
    content: string,
    extractionData: IntelligenceExtractionData,
    messageId: string,
    timestamp: string
  ): string {
    const entities = extractionData.entities.map(e => e.text).join(', ')
    const topics = extractionData.topics.map(t => t.name).join(', ')

    return `## Pinned Message - ${timestamp}

**Message ID**: ${messageId}
**Entities**: ${entities || 'None identified'}
**Topics**: ${topics || 'None identified'}

### Content
${content}

### Summary
${extractionData.summary}

---`
  }

  /**
   * Create extraction prompt for LLM
   */
  private createExtractionPrompt(content: string): string {
    return `Please analyze the following content and extract structured intelligence:

CONTENT TO ANALYZE:
${content}

Please provide a JSON response with the following structure:
{
  "entities": [
    {
      "name": "entity name",
      "type": "person|place|concept|technology|organization|other",
      "confidence": 0.8,
      "context": "surrounding context"
    }
  ],
  "topics": [
    {
      "name": "topic name",
      "relevance": 0.9,
      "category": "business|technology|research|finance|energy|healthcare|manufacturing|general"
    }
  ],
  "domain": "primary domain detected",
  "summary": "brief content summary"
}

Focus on extracting meaningful entities and topics. Support international languages including Chinese characters.`
  }

  /**
   * Call LLM for extraction
   */
  private async callLLMForExtraction(modelId: string, prompt: string, modelInfo: any): Promise<string> {
    console.log(`🔄 Calling ${modelInfo?.provider} model: ${modelId}`)

    if (modelInfo?.provider === 'ollama') {
      return this.callOllamaModel(modelId, prompt)
    } else if (modelInfo?.provider === 'lmstudio') {
      return this.callLMStudioModel(modelId, prompt)
    } else {
      // External model (OpenRouter)
      return this.callOpenRouterModel(modelId, prompt)
    }
  }

  /**
   * Call Ollama model
   */
  private async callOllamaModel(modelId: string, prompt: string): Promise<string> {
    const response = await fetch('http://localhost:11434/api/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json; charset=utf-8'
      },
      body: JSON.stringify({
        model: modelId.replace('ollama:', ''),
        prompt: prompt,
        stream: false
      })
    })

    if (!response.ok) {
      throw new Error(`Ollama API error: ${response.status}`)
    }

    const data = await response.json()
    return data.response || ''
  }

  /**
   * Call LM Studio model
   */
  private async callLMStudioModel(modelId: string, prompt: string): Promise<string> {
    const response = await fetch('http://localhost:1234/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json; charset=utf-8'
      },
      body: JSON.stringify({
        model: modelId.replace('lmstudio:', ''),
        messages: [
          { role: 'user', content: prompt }
        ],
        temperature: 0.3,
        max_tokens: 2000
      })
    })

    if (!response.ok) {
      throw new Error(`LM Studio API error: ${response.status}`)
    }

    const data = await response.json()
    return data.choices?.[0]?.message?.content || ''
  }

  /**
   * Call OpenRouter model
   */
  private async callOpenRouterModel(_modelId: string, _prompt: string): Promise<string> {
    // This would need API key from settings
    throw new Error('OpenRouter integration not implemented in test mode')
  }

  /**
   * Parse LLM response into structured data
   */
  private async parseLLMResponse(llmResponse: string, originalContent: string, attachments: any[]): Promise<IntelligenceExtractionData> {
    try {
      // Try to parse JSON response
      const jsonMatch = llmResponse.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0])

        return {
          entities: parsed.entities || [],
          topics: parsed.topics || [],
          artifacts: await this.extractArtifacts(originalContent, attachments)
        }
      }
    } catch (error) {
      console.error('Failed to parse LLM response:', error)
    }

    // Fallback: extract what we can from text response
    return await this.extractFromTextResponse(llmResponse, originalContent, attachments)
  }



  /**
   * Extract intelligence from text response when JSON parsing fails
   */
  private async extractFromTextResponse(_response: string, originalContent: string, attachments: any[]): Promise<IntelligenceExtractionData> {
    // Fallback to keyword-based extraction
    console.log('🔄 Falling back to keyword extraction due to LLM parsing failure')
    const result = await this.extractIntelligence(originalContent, attachments)
    return result.data
  }
}

export const intelligenceService = new IntelligenceService()
