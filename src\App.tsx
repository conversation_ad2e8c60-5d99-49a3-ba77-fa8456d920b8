import { useState, useEffect } from 'react'
import { useAppStore } from './store'
import IconBar from './components/IconBar'
import MobileNavBar from './components/MobileNavBar'
import Sidebar from './components/Sidebar'
import ChatArea from './components/ChatArea'
import HomePage from './pages/HomePage'
import HistoryPage from './pages/HistoryPage'
import SettingsPage from './pages/SettingsPage'
import FilesPage from './pages/FilesPage'
import PerformanceMonitor from './components/PerformanceMonitor'
import { ArtifactsSidebar } from './components/artifacts/ArtifactsSidebar'
import { ToastProvider, useArtifactToasts } from './components/artifacts/controls/ArtifactToast'
import { HashRouter as Router, Routes, Route } from 'react-router-dom'
import { useKeyboardShortcuts } from './hooks/useKeyboardShortcuts'
import WindowTopBar from './components/WindowTopBar'
import AppTopBar from './components/AppTopBar'
import NavigationWrapper from './components/NavigationWrapper'
import { performanceMonitor } from './services/performanceMonitor'
import { sharedDropboxService } from './services/sharedDropboxService'
import { useVerticalMode } from './hooks/useVerticalMode'
import NotificationSystem from './components/NotificationSystem'

// Component to handle model update notifications
function ModelUpdateListener() {
  const toasts = useArtifactToasts()

  useEffect(() => {
    const handleModelUpdate = (event: CustomEvent) => {
      const { message, type, updateTime, summary } = event.detail
      if (type === 'success') {
        toasts.success(message, 8000, updateTime, summary) // Show for 8 seconds with summary
      } else if (type === 'error') {
        toasts.error(message, 5000)
      } else {
        toasts.info(message, 5000)
      }
    }

    window.addEventListener('model-update-complete', handleModelUpdate as EventListener)

    return () => {
      window.removeEventListener('model-update-complete', handleModelUpdate as EventListener)
    }
  }, [toasts])

  return null
}

// Component to handle keyboard shortcuts inside Router context
function KeyboardShortcutsHandler() {
  useKeyboardShortcuts()
  return null
}

function App() {
  const { sidebarOpen, setSidebarOpen } = useAppStore()
  const [showPerformanceMonitor, setShowPerformanceMonitor] = useState(false)
  const isVerticalMode = useVerticalMode()

  // Initialize performance monitoring and shared dropbox
  useEffect(() => {
    performanceMonitor.startMonitoring()

    // Initialize shared dropbox service
    sharedDropboxService.initialize().catch(error => {
      console.error('Failed to initialize shared dropbox:', error)
    })

    return () => {
      performanceMonitor.stopMonitoring()
    }
  }, [])

  return (
    <Router>
      <ToastProvider>
        <NavigationWrapper>
          <ModelUpdateListener />
          <KeyboardShortcutsHandler />
          
          {/* Notification System for Smart Instructions */}
          <NotificationSystem />
          
        {/* Custom Window Top Bar */}
        <WindowTopBar />

        {/* Application Top Bar */}
        <AppTopBar />

        <div className="h-[calc(100vh-72px)] flex flex-col md:flex-row bg-gray-900 text-white font-sans antialiased selection:bg-primary/60 overflow-hidden">

        {/* Mobile Navigation Bar - Hidden in vertical mode */}
        {!isVerticalMode && <MobileNavBar />}

        {/* Desktop Layout */}
        <div className="flex flex-1 min-h-0">
          {/* VSCode-style Icon Bar - Hidden in vertical mode */}
          {!isVerticalMode && <IconBar />}

          <Routes>
            <Route path="/" element={
              <div className="flex-1 flex flex-col min-w-0 h-full">
                <HomePage />
              </div>
            } />
            <Route path="/chat" element={
              <>
                {/* Sidebar for Chat page */}
                <div className={`
                  ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
                  ${isVerticalMode ? 'fixed' : 'md:relative'} transition-transform duration-300 ease-in-out
                  z-40 h-full
                  ${sidebarOpen ? 'left-0' : 'left-0'}
                  ${isVerticalMode ? 'w-48' : 'md:left-0'}
                `}>
                  <Sidebar />
                </div>

                {/* Overlay for mobile and vertical mode */}
                {sidebarOpen && (isVerticalMode || window.innerWidth < 768) && (
                  <div
                    className="fixed inset-0 bg-black/50 z-30"
                    onClick={() => setSidebarOpen(false)}
                  />
                )}

                {/* Main content area */}
                <div className="flex-1 flex flex-col min-w-0 h-full">
                  <ChatArea />
                </div>

                {/* Artifacts Sidebar - Hidden in vertical mode */}
                {!isVerticalMode && <ArtifactsSidebar />}
              </>
            } />
            <Route path="/chat/:id" element={
              <>
                {/* Sidebar for Chat page */}
                <div className={`
                  ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
                  ${isVerticalMode ? 'fixed' : 'md:relative'} transition-transform duration-300 ease-in-out
                  z-40 h-full
                  ${sidebarOpen ? 'left-0' : 'left-0'}
                  ${isVerticalMode ? 'w-48' : 'md:left-0'}
                `}>
                  <Sidebar />
                </div>

                {/* Overlay for mobile and vertical mode */}
                {sidebarOpen && (isVerticalMode || window.innerWidth < 768) && (
                  <div
                    className="fixed inset-0 bg-black/50 z-30"
                    onClick={() => setSidebarOpen(false)}
                  />
                )}

                {/* Main content area */}
                <div className="flex-1 flex flex-col min-w-0 h-full">
                  <ChatArea />
                </div>

                {/* Artifacts Sidebar - Hidden in vertical mode */}
                {!isVerticalMode && <ArtifactsSidebar />}
              </>
            } />
            <Route path="/history" element={
              <>
                {/* Sidebar for History page */}
                <div className={`
                  ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
                  ${isVerticalMode ? 'fixed' : 'md:relative'} transition-transform duration-300 ease-in-out
                  z-40 h-full
                  ${sidebarOpen ? 'left-0' : 'left-0'}
                  ${isVerticalMode ? 'w-48' : 'md:left-0'}
                `}>
                  <Sidebar />
                </div>

                {/* Overlay for mobile and vertical mode */}
                {sidebarOpen && (isVerticalMode || window.innerWidth < 768) && (
                  <div
                    className="fixed inset-0 bg-black/50 z-30"
                    onClick={() => setSidebarOpen(false)}
                  />
                )}

                {/* Main content area */}
                <div className="flex-1 flex flex-col min-w-0 h-full">
                  <HistoryPage />
                </div>
              </>
            } />
            <Route path="/settings" element={
              <div className="flex-1 flex flex-col min-w-0 h-full">
                <SettingsPage />
              </div>
            } />
            <Route path="/files" element={
              <>
                <FilesPage />
                {/* Artifacts Sidebar for Files page - Hidden in vertical mode */}
                {!isVerticalMode && <ArtifactsSidebar />}
              </>
            } />
            <Route path="/files/:contextId" element={
              <>
                <FilesPage />
                {/* Artifacts Sidebar for Files page - Hidden in vertical mode */}
                {!isVerticalMode && <ArtifactsSidebar />}
              </>
            } />

          </Routes>
        </div>

        {/* Performance Monitor */}
        <PerformanceMonitor
          isVisible={showPerformanceMonitor}
          onToggle={() => setShowPerformanceMonitor(!showPerformanceMonitor)}
        />
        </div>
        </NavigationWrapper>
      </ToastProvider>
    </Router>
  )
}

export default App
