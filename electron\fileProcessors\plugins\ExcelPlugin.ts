/**
 * Excel Processing Plugin
 * Core plugin for processing Excel files using XLSX
 */

import * as fs from 'fs'
import * as path from 'path'
import { FileProcessorPlugin, ProcessedFileContent } from '../types'

export default class ExcelPlugin implements FileProcessorPlugin {
  name = 'ExcelPlugin'
  version = '1.0.0'
  description = 'Excel plugin - DISABLED for security (xlsx vulnerability)'
  author = 'ChatLo Team'
  dependencies = ['xlsx']
  optional = true
  
  private xlsxAvailable = false
  private XLSX: any = null

  async initialize(): Promise<void> {
    try {
      this.XLSX = require('xlsx')
      this.xlsxAvailable = true
      console.log('⚠️  ExcelPlugin: XLSX library loaded but DISABLED for security')
    } catch (error) {
      this.xlsxAvailable = false
      console.log('✅ ExcelPlugin: XLSX not available (security improvement)')
    }
  }

  supportedTypes = ['excel', 'spreadsheet']
  supportedExtensions = ['.xlsx', '.xls', '.csv', '.ods']

  canProcess(filePath: string, fileType: string): boolean {
    // Temporarily disabled for security - xlsx vulnerability
    return false
    
    // Original logic (disabled):
    // const extension = path.extname(filePath).toLowerCase()
    // return this.xlsxAvailable && (this.supportedTypes.includes(fileType) || this.supportedExtensions.includes(extension))
  }

  async process(filePath: string): Promise<ProcessedFileContent> {
    // Excel processing temporarily disabled for security
    const stats = await fs.promises.stat(filePath)
    const filename = path.basename(filePath)
    
    return {
      error: `Excel processing disabled for security (xlsx vulnerability). File: ${filename}`,
      metadata: {
        filename,
        fileSize: stats.size,
        lastModified: stats.mtime,
        processor: this.name,
        securityNote: 'Excel processing disabled due to GHSA-4r6h-8v6p-xvw6 and GHSA-5pgg-2g8v-p4x9 vulnerabilities in xlsx library',
        suggestedAction: 'Convert Excel file to CSV format for processing'
      }
    }
  }

  /* Original implementation (disabled for security):
    try {
      const stats = await fs.promises.stat(filePath)
      const extension = path.extname(filePath).toLowerCase()
      
      // Check file size (limit to 100MB for Excel files)
      const maxSize = 100 * 1024 * 1024 // 100MB
      if (stats.size > maxSize) {
        return {
          error: `Excel file too large: ${Math.round(stats.size / 1024 / 1024)}MB (max: 100MB)`
        }
      }

      if (!this.xlsxAvailable || !this.XLSX) {
        return {
          error: 'Excel processing not available - xlsx library not loaded'
        }
      }

      // Read the workbook
      const workbook = this.XLSX.readFile(filePath)
      
      // Process all sheets
      const sheetsData = this.processAllSheets(workbook)
      
      // Combine all text content
      const allText = sheetsData.sheets
        .map(sheet => `Sheet: ${sheet.name}\n${sheet.text}`)
        .join('\n\n')

      const metadata = {
        fileSize: stats.size,
        lastModified: stats.mtime,
        extension,
        processor: this.name,
        
        // Workbook structure
        sheetCount: workbook.SheetNames.length,
        sheetNames: workbook.SheetNames,
        
        // Content analysis
        totalRows: sheetsData.totalRows,
        totalCells: sheetsData.totalCells,
        totalNonEmptyCells: sheetsData.totalNonEmptyCells,
        
        // Sheet details
        sheets: sheetsData.sheets.map(sheet => ({
          name: sheet.name,
          rows: sheet.rows,
          cols: sheet.cols,
          nonEmptyCells: sheet.nonEmptyCells,
          hasHeaders: sheet.hasHeaders,
          dataTypes: sheet.dataTypes
        })),
        
        // File format info
        fileFormat: this.getFileFormat(extension),
        
        processingTime: Date.now()
      }

      return {
        text: allText,
        metadata
      }

    } catch (error: any) {
      console.error('Error processing Excel file:', error)
      
      // Provide helpful error messages
      if (error.message?.includes('Unsupported file')) {
        return {
          error: 'Unsupported Excel file format or corrupted file'
        }
      }

      return {
        error: `Failed to process Excel file: ${error?.message || 'Unknown error'}`
      }
    }
  }

  // Process all sheets in the workbook
  private processAllSheets(workbook: XLSX.WorkBook) {
    let totalRows = 0
    let totalCells = 0
    let totalNonEmptyCells = 0
    const sheets = []

    for (const sheetName of workbook.SheetNames) {
      const worksheet = workbook.Sheets[sheetName]
      const sheetData = this.processSheet(worksheet, sheetName)
      
      sheets.push(sheetData)
      totalRows += sheetData.rows
      totalCells += sheetData.totalCells
      totalNonEmptyCells += sheetData.nonEmptyCells
    }

    return {
      sheets,
      totalRows,
      totalCells,
      totalNonEmptyCells
    }
  }

  // Process a single sheet
  private processSheet(worksheet: XLSX.WorkSheet, sheetName: string) {
    // Convert to JSON to get structured data
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: '' })
    
    // Get sheet range
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1')
    const rows = range.e.r + 1
    const cols = range.e.c + 1
    const totalCells = rows * cols

    // Count non-empty cells
    let nonEmptyCells = 0
    const dataTypes = { string: 0, number: 0, boolean: 0, date: 0, formula: 0 }

    for (const cellAddress in worksheet) {
      if (cellAddress.startsWith('!')) continue // Skip metadata
      
      const cell = worksheet[cellAddress]
      if (cell && cell.v !== undefined && cell.v !== '') {
        nonEmptyCells++
        
        // Analyze data types
        if (cell.t === 's') dataTypes.string++
        else if (cell.t === 'n') dataTypes.number++
        else if (cell.t === 'b') dataTypes.boolean++
        else if (cell.t === 'd') dataTypes.date++
        else if (cell.f) dataTypes.formula++
      }
    }

    // Convert to text format
    const textRows = jsonData.map(row => 
      Array.isArray(row) ? row.join('\t') : String(row)
    ).filter(row => row.trim().length > 0)

    const text = textRows.join('\n')

    // Detect if first row might be headers
    const hasHeaders = this.detectHeaders(jsonData)

    return {
      name: sheetName,
      text,
      rows,
      cols,
      totalCells,
      nonEmptyCells,
      hasHeaders,
      dataTypes,
      textRows: textRows.length
    }
  }

  // Simple header detection
  private detectHeaders(jsonData: any[]): boolean {
    if (jsonData.length < 2) return false
    
    const firstRow = jsonData[0]
    const secondRow = jsonData[1]
    
    if (!Array.isArray(firstRow) || !Array.isArray(secondRow)) return false
    
    // Check if first row contains mostly strings and second row contains different types
    const firstRowTypes = firstRow.map(cell => typeof cell)
    const secondRowTypes = secondRow.map(cell => typeof cell)
    
    const firstRowStrings = firstRowTypes.filter(type => type === 'string').length
    const typesDiffer = firstRowTypes.some((type, i) => type !== secondRowTypes[i])
    
    return firstRowStrings > firstRow.length * 0.7 && typesDiffer
  }

  // Get file format description
  private getFileFormat(extension: string): string {
    const formats: Record<string, string> = {
      '.xlsx': 'Excel 2007+ (XLSX)',
      '.xls': 'Excel 97-2003 (XLS)',
      '.csv': 'Comma Separated Values',
      '.ods': 'OpenDocument Spreadsheet'
    }

    return formats[extension] || 'Unknown spreadsheet format'
  }
  */
}
