import React, { useState, useEffect, useRef } from 'react'


import { ContextVault, ContextFolder } from '../types'
import { contextVaultService } from '../services/contextVaultService'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'


interface ContextVaultSelectorProps {
  selectedContextId?: string
  onContextChange?: (contextId: string | null) => void
}

interface NewVaultFormData {
  name: string
  objective: string
  vaultType: 'Personal' | 'Work'
}

export const ContextVaultSelector: React.FC<ContextVaultSelectorProps> = ({
  selectedContextId,
  onContextChange
}) => {
  const [vaults, setVaults] = useState<ContextVault[]>([])
  const [currentVaultIndex, setCurrentVaultIndex] = useState(0)
  const [selectedContext, setSelectedContext] = useState<ContextFolder | null>(null)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [showNewVaultForm, setShowNewVaultForm] = useState(false)
  const [newVaultForm, setNewVaultForm] = useState<NewVaultFormData>({ name: '', objective: '', vaultType: 'Personal' })
  const [loading, setLoading] = useState(false)

  const dropdownRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    console.log('=== STEP 3 DEBUG: CONTEXT VAULT SELECTOR INIT ===')

    // Subscribe to context vault service
    const unsubscribe = contextVaultService.subscribe((updatedVaults, updatedSelectedContextId) => {
      console.log('🎯 ContextVaultSelector received update:')
      console.log('  - Vaults count:', updatedVaults.length)
      console.log('  - Selected context ID:', updatedSelectedContextId)
      console.log('  - Vaults data:', updatedVaults.map(v => ({
        name: v.name,
        path: v.path,
        contexts: v.contexts.map(c => ({ name: c.name, path: c.path }))
      })))

      setVaults(updatedVaults)

      // Update selected context if provided
      if (updatedSelectedContextId) {
        console.log('🔍 Finding context by ID:', updatedSelectedContextId)
        const result = contextVaultService.findContextById(updatedSelectedContextId)
        if (result) {
          console.log('✅ Context found and set:')
          console.log('  - Context name:', result.context.name)
          console.log('  - Context path:', result.context.path)
          console.log('  - Vault name:', result.vault.name)

          setSelectedContext(result.context)
          const vaultIndex = updatedVaults.indexOf(result.vault)
          if (vaultIndex >= 0) {
            setCurrentVaultIndex(vaultIndex)
          }
        } else {
          console.log('❌ Context not found for ID:', updatedSelectedContextId)
        }
      } else {
        console.log('📭 No selected context ID provided')
      }
    })

    console.log('🚀 Initializing context vault service...')
    // Initialize the service
    contextVaultService.initialize()

    return unsubscribe
  }, [])

  useEffect(() => {
    // Handle external selectedContextId changes
    if (selectedContextId) {
      const result = contextVaultService.findContextById(selectedContextId)
      if (result) {
        setSelectedContext(result.context)
        const vaultIndex = vaults.indexOf(result.vault)
        if (vaultIndex >= 0) {
          setCurrentVaultIndex(vaultIndex)
        }
      }
    }
  }, [selectedContextId, vaults])

  useEffect(() => {
    // Close dropdown when clicking outside
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const navigateVault = (direction: 'prev' | 'next') => {
    if (vaults.length === 0) return

    let newIndex = currentVaultIndex
    if (direction === 'prev') {
      newIndex = currentVaultIndex > 0 ? currentVaultIndex - 1 : vaults.length - 1
    } else {
      newIndex = currentVaultIndex < vaults.length - 1 ? currentVaultIndex + 1 : 0
    }

    setCurrentVaultIndex(newIndex)

    // Auto-select first context in new vault
    if (vaults[newIndex].contexts.length > 0) {
      const firstContext = vaults[newIndex].contexts[0]
      setSelectedContext(firstContext)
      contextVaultService.setSelectedContext(firstContext.id)
      onContextChange?.(firstContext.id)
    }
  }

  const selectContext = (context: ContextFolder | null) => {
    setSelectedContext(context)
    setIsDropdownOpen(false)

    if (context) {
      contextVaultService.setSelectedContext(context.id)
      onContextChange?.(context.id)
    } else {
      // Selecting shared dropbox (no context)
      contextVaultService.clearSelectedContext()
      onContextChange?.(null)
    }
  }

  const handleNewVault = async () => {
    console.log('=== FORM SUBMISSION DEBUG ===')
    console.log('Form data:', newVaultForm)
    console.log('Name trimmed:', newVaultForm.name.trim())

    if (!newVaultForm.name.trim()) {
      console.log('❌ Form validation failed - empty name')
      return
    }

    try {
      setLoading(true)
      console.log('🚀 Starting context creation...')

      const result = await contextVaultService.createContext({
        name: newVaultForm.name,
        objective: newVaultForm.objective,
        vaultType: newVaultForm.vaultType
      })

      console.log('📋 Context creation result:', result)

      if (result.success && result.context) {
        console.log('✅ Context creation successful')
        // Reset form and close modal
        setNewVaultForm({ name: '', objective: '', vaultType: 'Personal' })
        setShowNewVaultForm(false)

        // The context vault service will handle updating the UI and showing toast
        onContextChange?.(result.context.id)
        console.log('✅ Form reset and modal closed')
      } else {
        console.log('❌ Context creation failed or no context returned')
        console.log('Result details:', result)
      }

    } catch (error) {
      console.error('❌ Error creating new context:', error)
    } finally {
      setLoading(false)
      console.log('===============================')
    }
  }

  // Get all contexts from all vaults for the dropdown
  const getAllAvailableContexts = (): ContextFolder[] => {
    const allContexts: ContextFolder[] = []
    for (const vault of vaults) {
      allContexts.push(...vault.contexts)
    }
    return allContexts
  }

  const availableContexts = getAllAvailableContexts()

  return (
    <>
      <div className="p-4 border-b border-tertiary/50">
        <div className="flex items-center justify-between">
          {/* Navigation and Dropdown */}
          <div className="flex items-center gap-2 flex-1">
            <button
              onClick={() => navigateVault('prev')}
              className="p-1 hover:bg-gray-700 rounded transition-colors"
              disabled={vaults.length <= 1}
            >
              <FontAwesomeIcon icon={ICONS.chevronLeft} className="text-gray-400 text-xs" />
            </button>

            <div className="relative flex-1" ref={dropdownRef}>
              <button
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                className="w-full flex items-center justify-between p-2 hover:bg-gray-700/50 rounded transition-colors"
              >
                <span className="font-medium text-supplement1 text-sm truncate">
                  {selectedContext?.name || 'Select Context'}
                </span>
                <FontAwesomeIcon
                  icon={ICONS.chevronDown}
                  className={`text-gray-400 text-xs transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`}
                />
              </button>

              {isDropdownOpen && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-gray-800 border border-tertiary/50 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto">
                  {/* Shared Dropbox Option */}
                  <button
                    onClick={() => selectContext(null)}
                    className={`w-full text-left p-3 hover:bg-gray-700/50 transition-colors flex items-center gap-2 border-b border-tertiary/30 ${
                      !selectedContext ? 'bg-primary/20 text-primary' : 'text-supplement1'
                    }`}
                  >
                    <FontAwesomeIcon icon={ICONS.folder} className="text-xs" />
                    <span className="text-sm font-medium">📦 Shared Dropbox</span>
                    <span className="text-xs text-gray-400 ml-auto">No context selected</span>
                  </button>

                  {/* Group contexts by vault type */}
                  {vaults.map((vault) => (
                    <div key={vault.id}>
                      {/* Vault header */}
                      <div className="px-3 py-2 text-xs font-medium text-gray-400 bg-gray-700/50 border-b border-tertiary/30">
                        {vault.name}
                      </div>
                      {/* Vault contexts */}
                      {vault.contexts.map((context) => (
                        <button
                          key={context.id}
                          onClick={() => selectContext(context)}
                          className={`w-full text-left p-3 hover:bg-gray-700/50 transition-colors flex items-center gap-2 ${
                            selectedContext?.id === context.id ? 'bg-primary/20 text-primary' : 'text-supplement1'
                          }`}
                        >
                          <FontAwesomeIcon icon={ICONS.folder} className="text-xs" />
                          <span className="text-sm">{context.name}</span>
                        </button>
                      ))}
                    </div>
                  ))}

                  {/* Show message if no contexts available */}
                  {availableContexts.length === 0 && (
                    <div className="p-3 text-center text-gray-400 text-sm">
                      No context vaults found. Create one to get started.
                    </div>
                  )}
                </div>
              )}
            </div>

            <button
              onClick={() => navigateVault('next')}
              className="p-1 hover:bg-gray-700 rounded transition-colors"
              disabled={vaults.length <= 1}
            >
              <FontAwesomeIcon icon={ICONS.chevronRight} className="text-gray-400 text-xs" />
            </button>
          </div>

          {/* Add New Vault Button */}
          <button
            onClick={() => setShowNewVaultForm(true)}
            className="p-1 hover:bg-gray-700 rounded transition-colors group relative ml-2"
          >
            <FontAwesomeIcon icon={ICONS.plus} className="text-gray-400 text-xs" />
            <div className="absolute bottom-6 right-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              New Context
            </div>
          </button>
        </div>
      </div>

      {/* New Vault Form Overlay */}
      {showNewVaultForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-96 max-w-[90vw]">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-supplement1">New Context</h3>
              <button
                onClick={() => setShowNewVaultForm(false)}
                className="p-1 hover:bg-gray-700 rounded transition-colors"
              >
                <FontAwesomeIcon icon={ICONS.times} className="text-gray-400 text-sm" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-supplement1 mb-2">
                  Vault Type <span className="text-secondary">*</span>
                </label>
                <div className="flex gap-2">
                  <button
                    type="button"
                    onClick={() => setNewVaultForm(prev => ({ ...prev, vaultType: 'Personal' }))}
                    className={`flex-1 p-3 rounded-lg border transition-colors ${
                      newVaultForm.vaultType === 'Personal'
                        ? 'bg-primary/20 border-primary text-primary'
                        : 'bg-gray-700 border-tertiary/50 text-supplement1 hover:bg-gray-600'
                    }`}
                  >
                    Personal
                  </button>
                  <button
                    type="button"
                    onClick={() => setNewVaultForm(prev => ({ ...prev, vaultType: 'Work' }))}
                    className={`flex-1 p-3 rounded-lg border transition-colors ${
                      newVaultForm.vaultType === 'Work'
                        ? 'bg-secondary/20 border-secondary text-secondary'
                        : 'bg-gray-700 border-tertiary/50 text-supplement1 hover:bg-gray-600'
                    }`}
                  >
                    Work
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-supplement1 mb-2">
                  Name <span className="text-secondary">*</span>
                </label>
                <input
                  type="text"
                  value={newVaultForm.name}
                  onChange={(e) => setNewVaultForm(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full p-3 bg-gray-700 border border-tertiary/50 rounded-lg text-supplement1 placeholder-gray-400 focus:outline-none focus:border-primary"
                  placeholder="Enter context name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-supplement1 mb-2">
                  Objective (optional)
                </label>
                <textarea
                  value={newVaultForm.objective}
                  onChange={(e) => setNewVaultForm(prev => ({ ...prev, objective: e.target.value }))}
                  className="w-full p-3 bg-gray-700 border border-tertiary/50 rounded-lg text-supplement1 placeholder-gray-400 focus:outline-none focus:border-primary resize-none"
                  rows={3}
                  placeholder="Describe the purpose of this context"
                />
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <button
                onClick={() => setShowNewVaultForm(false)}
                className="flex-1 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-supplement1 rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleNewVault}
                disabled={!newVaultForm.name.trim() || loading}
                className="flex-1 px-4 py-2 bg-primary hover:bg-primary/80 text-gray-900 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Creating...' : 'Create Context'}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
