import React from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { IconDefinition } from '@fortawesome/fontawesome-svg-core'
import { IconName, getIcon } from './index'

interface IconProps {
  name?: IconName
  icon?: IconDefinition
  className?: string
  size?: 'xs' | 'sm' | 'lg' | 'xl' | '2xl'
  spin?: boolean
  pulse?: boolean
  onClick?: () => void
}

/**
 * Centralized Icon component using the icon registry
 * Rule 2.2 compliant - uses only local FontAwesome icons
 */
export const Icon: React.FC<IconProps> = ({
  name,
  icon,
  className = '',
  size,
  spin = false,
  pulse = false,
  onClick
}) => {
  // Get icon from registry by name or use provided icon
  const iconToUse = name ? getIcon(name) : icon
  
  if (!iconToUse) {
    console.warn('Icon: No icon provided or invalid icon name')
    return null
  }

  const sizeClass = size ? `fa-${size}` : ''
  const spinClass = spin ? 'fa-spin' : ''
  const pulseClass = pulse ? 'fa-pulse' : ''
  
  const combinedClassName = [
    className,
    sizeClass,
    spinClass,
    pulseClass
  ].filter(Boolean).join(' ')

  return (
    <FontAwesomeIcon
      icon={iconToUse}
      className={combinedClassName}
      onClick={onClick}
    />
  )
}

export default Icon
