import React from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'
import { useNavigation } from '../hooks/useNavigation'
import { BreadcrumbItem } from '../services/navigationService'

interface BreadcrumbNavigationProps {
  className?: string
  showIcons?: boolean
  maxItems?: number
}

const BreadcrumbNavigation: React.FC<BreadcrumbNavigationProps> = ({
  className = '',
  showIcons = true,
  maxItems = 5
}) => {
  const { getBreadcrumbs, navigate } = useNavigation()
  const breadcrumbs = getBreadcrumbs()

  // Truncate breadcrumbs if too many
  const displayBreadcrumbs = breadcrumbs.length > maxItems 
    ? [
        breadcrumbs[0], // Always show home
        { label: '...', link: '', icon: '' }, // Ellipsis
        ...breadcrumbs.slice(-(maxItems - 2)) // Show last items
      ]
    : breadcrumbs

  const getIconForBreadcrumb = (iconName: string) => {
    const iconMap: Record<string, any> = {
      'fa-home': ICONS.home,
      'fa-comment': ICONS.comment,
      'fa-folder': ICONS.folder,
      'fa-clock': ICONS.clock,
      'fa-gear': ICONS.settings,
      'fa-circle': ICONS.circle,
      'fa-file': ICONS.file,
      'fa-file-text': ICONS.fileText,
      'fa-file-code': ICONS.fileCode,
      'fa-file-image': ICONS.fileImage,
      'fa-file-pdf': ICONS.filePdf
    }
    return iconMap[iconName] || ICONS.file
  }

  const handleBreadcrumbClick = (item: BreadcrumbItem) => {
    if (item.link && item.label !== '...') {
      navigate(item.link)
    }
  }

  if (breadcrumbs.length === 0) {
    return null
  }

  return (
    <nav 
      className={`flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400 ${className}`}
      aria-label="Breadcrumb navigation"
    >
      {displayBreadcrumbs.map((item, index) => (
        <React.Fragment key={`${item.label}-${index}`}>
          {index > 0 && (
            <FontAwesomeIcon
              icon={ICONS.chevronRight}
              className="h-3 w-3 text-gray-400 dark:text-gray-500"
            />
          )}
          
          {item.label === '...' ? (
            <span className="text-gray-400 dark:text-gray-500 px-2">...</span>
          ) : (
            <button
              onClick={() => handleBreadcrumbClick(item)}
              className={`
                flex items-center space-x-1.5 px-2 py-1 rounded-md transition-colors
                ${index === displayBreadcrumbs.length - 1
                  ? 'text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/20 cursor-default'
                  : 'hover:text-indigo-600 dark:hover:text-indigo-400 hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer'
                }
              `}
              disabled={index === displayBreadcrumbs.length - 1}
            >
              {showIcons && item.icon && (
                <FontAwesomeIcon 
                  icon={getIconForBreadcrumb(item.icon)}
                  className="h-3 w-3"
                />
              )}
              <span className="truncate max-w-32">{item.label}</span>
            </button>
          )}
        </React.Fragment>
      ))}
    </nav>
  )
}

export default BreadcrumbNavigation
