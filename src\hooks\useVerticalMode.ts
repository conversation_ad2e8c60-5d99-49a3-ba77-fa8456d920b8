import { useState, useEffect } from 'react'

export const useVerticalMode = () => {
  const [isVerticalMode, setIsVerticalMode] = useState(false)

  useEffect(() => {
    const checkVerticalMode = () => {
      // Consider vertical mode when width is less than 600px or aspect ratio is close to 10:16
      const width = window.innerWidth
      const height = window.innerHeight
      const aspectRatio = width / height
      
      // Vertical mode: width < 600px OR aspect ratio < 0.8 (closer to 10:16 than 16:9)
      const isVertical = width < 600 || aspectRatio < 0.8
      setIsVerticalMode(isVertical)
    }

    // Check on mount
    checkVerticalMode()

    // Check on resize
    window.addEventListener('resize', checkVerticalMode)
    
    return () => {
      window.removeEventListener('resize', checkVerticalMode)
    }
  }, [])

  return isVerticalMode
} 